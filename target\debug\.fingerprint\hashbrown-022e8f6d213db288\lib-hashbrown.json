{"rustc": 3926191382657067107, "features": "[\"default-hasher\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 16533699616974903702, "path": 14697557671225509567, "deps": [[10842263908529601448, "<PERSON><PERSON><PERSON>", false, 18152937935742653444]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-022e8f6d213db288\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}