use bevy::prelude::*;
use bevy::pbr::CascadeShadowConfigBuilder;
use bevy::input::mouse::MouseMotion;
use std::f32::consts::PI;

fn main() {
    App::new()
        .add_plugins(DefaultPlugins)
        .add_systems(Startup, setup)
        .add_systems(Update, (
            player_movement,
            car_physics,
            camera_follow,
            interaction_system,
            update_ui,
        ))
        .run();
}

// Components
#[derive(Component)]
struct Player {
    speed: f32,
    is_driving: bool,
}

#[derive(Component)]
struct Car {
    speed: f32,
    max_speed: f32,
    acceleration: f32,
    turn_speed: f32,
    occupied: bool,
}

#[derive(Component)]
struct CameraController {
    distance: f32,
    height: f32,
    sensitivity: f32,
    yaw: f32,
    pitch: f32,
}

#[derive(Component)]
struct Ground;

#[derive(Component)]
struct InteractionUI;

fn setup(
    mut commands: Commands,
    mut meshes: ResMut>,
    mut materials: ResMut>,
) {
    // Ground
    commands.spawn((
        PbrBundle {
            mesh: meshes.add(Plane3d::default().mesh().size(100.0, 100.0)),
            material: materials.add(StandardMaterial {
                base_color: Color::srgb(0.3, 0.5, 0.3),
                perceptual_roughness: 0.9,
                ..default()
            }),
            ..default()
        },
        Ground,
    ));

    // Player (Capsule shape)
    commands.spawn((
        PbrBundle {
            mesh: meshes.add(Capsule3d::new(0.5, 2.0)),
            material: materials.add(StandardMaterial {
                base_color: Color::srgb(0.2, 0.3, 0.8),
                ..default()
            }),
            transform: Transform::from_xyz(0.0, 1.0, 0.0),
            ..default()
        },
        Player {
            speed: 5.0,
            is_driving: false,
        },
    ));

    // Car (simplified box shape with wheels)
    let car_body = commands.spawn((
        PbrBundle {
            mesh: meshes.add(Cuboid::new(2.0, 1.0, 4.0)),
            material: materials.add(StandardMaterial {
                base_color: Color::srgb(0.8, 0.1, 0.1),
                metallic: 0.7,
                ..default()
            }),
            transform: Transform::from_xyz(5.0, 0.5, 0.0),
            ..default()
        },
        Car {
            speed: 0.0,
            max_speed: 20.0,
            acceleration: 10.0,
            turn_speed: 2.0,
            occupied: false,
        },
    )).id();

    // Add wheels to the car
    let wheel_material = materials.add(StandardMaterial {
        base_color: Color::srgb(0.1, 0.1, 0.1),
        ..default()
    });
    
    let wheel_positions = [
        Vec3::new(0.8, -0.3, 1.5),
        Vec3::new(-0.8, -0.3, 1.5),
        Vec3::new(0.8, -0.3, -1.5),
        Vec3::new(-0.8, -0.3, -1.5),
    ];

    for pos in wheel_positions.iter() {
        commands.spawn(PbrBundle {
            mesh: meshes.add(Cylinder::new(0.4, 0.3)),
            material: wheel_material.clone(),
            transform: Transform::from_translation(*pos)
                .with_rotation(Quat::from_rotation_z(PI / 2.0)),
            ..default()
        }).set_parent(car_body);
    }

    // Camera
    commands.spawn((
        Camera3dBundle {
            transform: Transform::from_xyz(0.0, 10.0, 10.0)
                .looking_at(Vec3::ZERO, Vec3::Y),
            ..default()
        },
        CameraController {
            distance: 10.0,
            height: 8.0,
            sensitivity: 0.5,
            yaw: 0.0,
            pitch: -0.3,
        },
    ));

    // Light
    commands.spawn(DirectionalLightBundle {
        directional_light: DirectionalLight {
            illuminance: 15000.0,
            shadows_enabled: true,
            ..default()
        },
        transform: Transform::from_rotation(Quat::from_euler(
            EulerRot::XYZ,
            -PI / 4.0,
            PI / 4.0,
            0.0,
        )),
        cascade_shadow_config: CascadeShadowConfigBuilder {
            num_cascades: 3,
            first_cascade_far_bound: 5.0,
            maximum_distance: 30.0,
            ..default()
        }.into(),
        ..default()
    });

    // Ambient light
    commands.insert_resource(AmbientLight {
        color: Color::WHITE,
        brightness: 0.3,
    });

    // UI Text
    commands.spawn((
        TextBundle::from_section(
            "Press E to enter/exit car",
            TextStyle {
                font_size: 20.0,
                color: Color::WHITE,
                ..default()
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            top: Val::Px(10.0),
            left: Val::Px(10.0),
            ..default()
        }),
        InteractionUI,
    ));
}

fn player_movement(
    time: Res,
    keyboard: Res>,
    mut query: Query<(&mut Transform, &Player), Without>,
    camera: Query<&Transform, (With, Without)>,
) {
    for (mut transform, player) in query.iter_mut() {
        if player.is_driving {
            continue;
        }

        let camera_transform = camera.single();
        let forward = camera_transform.forward().normalize();
        let right = camera_transform.right().normalize();
        
        // Project movement to ground plane
        let forward = Vec3::new(forward.x, 0.0, forward.z).normalize();
        let right = Vec3::new(right.x, 0.0, right.z).normalize();

        let mut movement = Vec3::ZERO;

        if keyboard.pressed(KeyCode::KeyW) {
            movement += forward;
        }
        if keyboard.pressed(KeyCode::KeyS) {
            movement -= forward;
        }
        if keyboard.pressed(KeyCode::KeyA) {
            movement -= right;
        }
        if keyboard.pressed(KeyCode::KeyD) {
            movement += right;
        }

        let speed = if keyboard.pressed(KeyCode::ShiftLeft) {
            player.speed * 2.0
        } else {
            player.speed
        };

        if movement.length() > 0.0 {
            movement = movement.normalize();
            transform.translation += movement * speed * time.delta_seconds();
            
            // Face movement direction
            if movement.length_squared() > 0.001 {
                transform.look_to(movement, Vec3::Y);
            }
        }

        // Jump
        if keyboard.just_pressed(KeyCode::Space) && transform.translation.y <= 1.1 {
            // Simple jump simulation
            transform.translation.y = 3.0;
        }

        // Gravity (simple)
        if transform.translation.y > 1.0 {
            transform.translation.y -= 5.0 * time.delta_seconds();
        } else {
            transform.translation.y = 1.0;
        }
    }
}

fn car_physics(
    time: Res,
    keyboard: Res>,
    mut query: Query<(&mut Transform, &mut Car)>,
    mut player_query: Query<(&mut Transform, &Player), Without>,
) {
    for (mut car_transform, mut car) in query.iter_mut() {
        if !car.occupied {
            // Gradually stop the car if unoccupied
            car.speed *= 0.95;
        } else {
            // Car controls
            if keyboard.pressed(KeyCode::KeyW) {
                car.speed += car.acceleration * time.delta_seconds();
            }
            if keyboard.pressed(KeyCode::KeyS) {
                car.speed -= car.acceleration * time.delta_seconds();
            }
            if keyboard.pressed(KeyCode::Space) {
                car.speed *= 0.9;
            }

            // Clamp speed
            car.speed = car.speed.clamp(-car.max_speed * 0.5, car.max_speed);

            // Turning
            if car.speed.abs() > 0.1 {
                let turn_factor = (car.speed.abs() / car.max_speed).min(1.0);
                if keyboard.pressed(KeyCode::KeyA) {
                    car_transform.rotate_y(car.turn_speed * turn_factor * time.delta_seconds());
                }
                if keyboard.pressed(KeyCode::KeyD) {
                    car_transform.rotate_y(-car.turn_speed * turn_factor * time.delta_seconds());
                }
            }
        }

        // Apply movement
        if car.speed.abs() > 0.01 {
            let forward = car_transform.forward();
            car_transform.translation += forward * car.speed * time.delta_seconds();
            
            // Friction
            car.speed *= 0.98;
        }

        // Keep car on ground
        car_transform.translation.y = 0.5;

        // Update player position if driving
        if car.occupied {
            if let Ok((mut player_transform, player)) = player_query.get_single_mut() {
                if player.is_driving {
                    player_transform.translation = car_transform.translation + Vec3::Y * 1.5;
                    player_transform.rotation = car_transform.rotation;
                }
            }
        }
    }
}

fn camera_follow(
    mut camera: Query<(&mut Transform, &mut CameraController), With>,
    player: Query<&Transform, (With, Without)>,
    car: Query<(&Transform, &Car), Without>,
    mut mouse_motion: EventReader,
    buttons: Res>,
) {
    if let Ok(player_transform) = player.get_single() {
        if let Ok((mut cam_transform, mut controller)) = camera.get_single_mut() {
            // Mouse camera control
            if buttons.pressed(MouseButton::Right) {
                for event in mouse_motion.read() {
                    controller.yaw -= event.delta.x * controller.sensitivity * 0.01;
                    controller.pitch -= event.delta.y * controller.sensitivity * 0.01;
                    controller.pitch = controller.pitch.clamp(-1.0, 0.3);
                }
            }

            // Find target position
            let target = if let Ok((car_transform, car)) = car.get_single() {
                if car.occupied {
                    car_transform.translation
                } else {
                    player_transform.translation
                }
            } else {
                player_transform.translation
            };

            // Calculate camera position
            let offset = Vec3::new(
                controller.yaw.sin() * controller.distance,
                controller.height,
                controller.yaw.cos() * controller.distance,
            );

            cam_transform.translation = target + offset;
            cam_transform.look_at(target, Vec3::Y);
        }
    }
}

fn interaction_system(
    keyboard: Res>,
    mut player_query: Query<(&Transform, &mut Player)>,
    mut car_query: Query<(&Transform, &mut Car), Without>,
) {
    if keyboard.just_pressed(KeyCode::KeyE) {
        if let Ok((player_transform, mut player)) = player_query.get_single_mut() {
            if let Ok((car_transform, mut car)) = car_query.get_single_mut() {
                let distance = player_transform.translation.distance(car_transform.translation);
                
                if !player.is_driving && distance < 3.0 {
                    // Enter car
                    player.is_driving = true;
                    car.occupied = true;
                } else if player.is_driving {
                    // Exit car
                    player.is_driving = false;
                    car.occupied = false;
                }
            }
        }
    }
}

fn update_ui(
    mut ui_query: Query<&mut Text, With>,
    player_query: Query<(&Transform, &Player)>,
    car_query: Query<&Transform, (With, Without)>,
) {
    if let Ok(mut text) = ui_query.get_single_mut() {
        if let Ok((player_transform, player)) = player_query.get_single() {
            if let Ok(car_transform) = car_query.get_single() {
                let distance = player_transform.translation.distance(car_transform.translation);
                
                if player.is_driving {
                    text.sections[0].value = "Press E to exit car | WASD to drive | Space to brake".to_string();
                } else if distance < 3.0 {
                    text.sections[0].value = "Press E to enter car".to_string();
                } else {
                    text.sections[0].value = "WASD to move | Shift to run | Space to jump".to_string();
                }
            }
        }
    }
}